# https://deepwiki.com/search/how-run-a-docker-service-in-do_06324ae9-3d9c-4d7b-a588-aef2455acffa
services:
  openhands:
    image: docker.all-hands.dev/all-hands-ai/openhands:0.39
    container_name: openhands-app
    ports:
      - "${OPENHANDS_PORT}:3000"
    env_file:
      - .env.ai
    environment:
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=${SANDBOX_RUNTIME_CONTAINER_IMAGE:-docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik}
      - LOG_ALL_EVENTS=${LOG_ALL_EVENTS:-true}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ~/.openhands-state:/.openhands-state
    extra_hosts:
      - "host.docker.internal:host-gateway"
    stdin_open: true
    privileged: true
    tty: true

volumes:
  ollama_data:
