FROM php:8.4 AS build

# Install dependencies in a single layer and clean up
RUN apt-get update && apt-get install -y \
    zip \
    unzip \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
WORKDIR /var/www/html

# Copy composer files first for better layer caching
COPY composer.json composer.lock ./
RUN composer install --no-dev --ignore-platform-reqs --no-interaction --optimize-autoloader --no-scripts

# Copy source code and run post-install scripts
COPY . .
RUN composer run-script post-autoload-dump --no-interaction

FROM node:20.11-alpine AS front

WORKDIR /var/www/html

# Copy package files first for better layer caching
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy source code and build
COPY . .
COPY --from=build /var/www/html/vendor /var/www/html/vendor
RUN npm run build

FROM php:8.4-fpm-alpine AS base

# Install PHP extension installer
RUN curl -sSLf \
    -o /usr/local/bin/install-php-extensions \
    https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions && \
    chmod +x /usr/local/bin/install-php-extensions

# Install system dependencies and PHP extensions in single layer
RUN apk update && apk add --no-cache \
    postgresql-client \
    curl \
    && install-php-extensions \
        curl \
        pdo_pgsql \
        pgsql \
        intl \
        zip \
        pcntl \
        bcmath \
    && rm -rf /var/cache/apk/* /tmp/*

COPY docker/php-fpm/docker.conf /usr/local/etc/php-fpm.d/docker.conf
COPY docker/php-fpm/php8.3.fpm.conf /usr/local/etc/php-fpm.d/zz-fpm.conf
COPY docker/php-fpm/php8.3.opcache.conf /usr/local/etc/php/php-fpm.d/zz-opcache.ini
COPY docker/php-fpm/php.ini /usr/local/etc/php/conf.d/app.ini
COPY docker/php-fpm/php8.3.opcache.conf /usr/local/etc/php/conf.d/zz-php8.3.opcache.conf

FROM base AS prod

# Install OPcache for production optimization
RUN install-php-extensions opcache && rm -rf /var/cache/apk/* /tmp/*

# Copy application files
COPY --chown=www-data:www-data . .
COPY --from=build --chown=www-data:www-data /var/www/html/vendor /var/www/html/vendor
#COPY --from=front --chown=www-data:www-data /var/www/html/public/build /var/www/html/public/build

# Configure environment and clean up in single layer
RUN mv .env.prod .env && \
    rm -f .env.* && \
    rm -rf /tmp/* /var/cache/apk/* /var/tmp/* /root/.composer /root/.npm /root/.cache /root/.config

# Set up entrypoint
COPY entrypoint-prod /usr/local/bin/entrypoint
RUN chmod +x /usr/local/bin/entrypoint

# Switch to non-root user
USER www-data
WORKDIR /var/www/html

# Add healthcheck to verify PHP-FPM is ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD php-fpm -t || exit 1

EXPOSE 9000
ENTRYPOINT ["/usr/local/bin/entrypoint"]
CMD ["php-fpm"]

FROM base AS dev

# Development dependencies and user setup in optimized layers
ARG LOCAL_USER=salesto
ARG USERID=1000
ARG GROUPID=1000
ENV LOCAL_USER=$LOCAL_USER

# Install development tools and create user in single layer
RUN apk add --no-cache git sudo && \
    install-php-extensions xdebug && \
    echo "CURRENT_USER: ${LOCAL_USER}" && \
    echo "GROUPID: ${GROUPID}" && \
    echo "USERID: ${USERID}" && \
    EXISTING_GROUP_NAME=$(getent group ${GROUPID} | cut -d: -f1) && \
    if [ -z "$EXISTING_GROUP_NAME" ]; then \
        addgroup -g ${GROUPID} ${LOCAL_USER} && \
        GROUP_TO_USE=${LOCAL_USER}; \
    else \
        GROUP_TO_USE=${EXISTING_GROUP_NAME}; \
    fi && \
    EXISTING_USER_NAME=$(getent passwd ${USERID} | cut -d: -f1) && \
    echo "EXISTING_USER_NAME: ${EXISTING_USER_NAME}" && \
    echo "GROUP_TO_USE: ${GROUP_TO_USE}" && \
    if [ -n "${EXISTING_USER_NAME}" ]; then deluser ${EXISTING_USER_NAME}; fi && \
    adduser -D -u ${USERID} -G ${GROUP_TO_USE} ${LOCAL_USER} && \
    git config --global --add safe.directory /var/www/html && \
    rm -rf /var/cache/apk/* /tmp/*

# Copy development configuration
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
COPY docker/php-fpm/php8.3.dev.conf /usr/local/etc/php-fpm.d/zz-dev.conf

# Set up development entrypoint
COPY entrypoint-dev /usr/local/bin/entrypoint
RUN chmod +x /usr/local/bin/entrypoint

# Switch to development user
USER $LOCAL_USER

ENV PATH="$PATH:/home/<USER>/.composer/vendor/bin"
WORKDIR /var/www/html

# Add healthcheck to verify composer dependencies are installed and Laravel is ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=180s --retries=5 \
    CMD test -d vendor && php artisan --version || exit 1

ENTRYPOINT ["/usr/local/bin/entrypoint"]
CMD ["php-fpm"]

FROM nginx:alpine AS http-base

# Install dependencies and configure nginx in single layer
ARG PHP_UPSTREAM_CONTAINER=php
ARG PHP_UPSTREAM_PORT=9000

RUN apk update && apk upgrade && \
    apk add --no-cache \
        logrotate \
        openssl \
        bash \
        curl \
    && if ! getent group www-data >/dev/null 2>&1; then addgroup -g 82 -S www-data; fi \
    && if ! getent passwd www-data >/dev/null 2>&1; then adduser -u 82 -D -S -G www-data www-data; fi \
    && touch /var/log/messages \
    && echo "upstream php-upstream { server ${PHP_UPSTREAM_CONTAINER}:${PHP_UPSTREAM_PORT}; }" > /etc/nginx/conf.d/upstream.conf \
    && rm /etc/nginx/conf.d/default.conf \
    && rm -rf /var/cache/apk/* /tmp/*

# Copy configuration files
COPY docker/nginx/nginx.conf /etc/nginx/
COPY docker/nginx/logrotate/nginx /etc/logrotate.d/
COPY docker/nginx/site.conf /etc/nginx/sites-available/site.conf

# Copy and prepare startup script
COPY docker/nginx/startup.sh /opt/startup.sh
RUN sed -i 's/\r//g' /opt/startup.sh && \
    chmod +x /opt/startup.sh

CMD ["/bin/bash", "/opt/startup.sh"]

FROM http-base AS http-prod

COPY --from=prod /var/www/html/public /var/www/html/public

# Add healthcheck to verify Laravel application is responding
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/up || exit 1

EXPOSE 80 81 443

# Sandbox stage based on Ubuntu for better compatibility and package management
FROM ubuntu:22.04 AS sandbox

LABEL org.opencontainers.image.authors="Salesto Team"

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies and Python for orchestration
RUN apt-get update && apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    git \
    sudo \
    bash \
    python3 \
    python3-dev \
    python3-pip \
    python3-venv \
    build-essential \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20 LTS (latest LTS with corepack support)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install Docker
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin \
    && rm -rf /var/lib/apt/lists/*

# Create a virtual environment for Python tools and install them there
RUN python3 -m venv /opt/python-tools \
    && /opt/python-tools/bin/pip install --no-cache-dir --upgrade pip \
    && /opt/python-tools/bin/pip install --no-cache-dir pipenv uv poetry \
    && npm install -g yarn \
    && corepack enable \
    && rm -rf /tmp/* /root/.cache

# Disable corepack download prompts
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0

# Add Python tools to PATH
ENV PATH="/opt/python-tools/bin:$PATH"

# Create user and group for all-hands access
ARG SANDBOX_USER=allhands
ARG USERID=1000
ARG GROUPID=1000
ENV SANDBOX_USER=$SANDBOX_USER

RUN groupadd -g $GROUPID $SANDBOX_USER \
    && useradd -u $USERID -g $SANDBOX_USER -s /bin/bash -m $SANDBOX_USER \
    && (getent group docker >/dev/null || groupadd docker) \
    && usermod -aG docker $SANDBOX_USER \
    && usermod -aG sudo $SANDBOX_USER \
    && echo "$SANDBOX_USER ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create workspace directory
RUN mkdir -p /workspace && chown -R $SANDBOX_USER:$SANDBOX_USER /workspace

# Copy setup scripts and directory
COPY setup.sh /workspace/setup.sh
COPY setup/ /workspace/setup/
RUN chmod +x /workspace/setup.sh \
    && find /workspace/setup/ -name "*.sh" -exec chmod +x {} \; \
    && chown -R $SANDBOX_USER:$SANDBOX_USER /workspace/setup/

# Copy and setup entrypoint script for sandbox
COPY entrypoint-sandbox /usr/local/bin/sandbox-entrypoint.sh
RUN chmod +x /usr/local/bin/sandbox-entrypoint.sh

# Set working directory
WORKDIR /workspace

# Set environment variables
ENV DOCKER_HOST=unix:///var/run/docker.sock
ENV PATH="$PATH:/home/<USER>/.local/bin"
ENV PYTHONPATH="/usr/lib/python3.12/site-packages"

# Add healthcheck to verify all services are ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=5 \
    CMD docker info && python3 --version && node --version || exit 1

EXPOSE 2375 2376

ENTRYPOINT ["/usr/local/bin/sandbox-entrypoint.sh"]
CMD []

