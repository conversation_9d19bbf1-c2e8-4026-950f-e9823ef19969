#!/bin/bash

# setup-application.sh - Application setup script
# This script handles building and starting the application

# Source the toolkit
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/toolkit.sh"

# Function to build Docker images
build_images() {
    log_info "Building Docker images..."

    if task build; then
        log_success "Docker images built successfully"
        return 0
    else
        log_error "Failed to build Docker images"
        return 1
    fi
}

# Function to start the application
start_application() {
    log_info "Starting the application..."

    if task up; then
        log_success "Application started successfully"
        return 0
    else
        log_error "Failed to start the application"
        return 1
    fi
}

# Function to wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."

    for i in {1..10}; do
        log_info "Check iteration $i/10"

        docker compose ps

        all_healthy=true

        # Check if all services are running and healthy
        for service in $(docker compose config --services); do
            if docker compose ps --services --filter "status=running" | grep -q "$service"; then
                log_success "$service container is running"

                # Check health status if healthcheck is configured
                health_status=$(docker compose ps --format "table {{.Service}}\t{{.Status}}" | grep "$service" | awk '{print $2}')
                if echo "$health_status" | grep -q "healthy"; then
                    log_success "$service container is healthy"
                elif echo "$health_status" | grep -q "unhealthy"; then
                    log_warning "$service container is unhealthy"
                    log_info "Displaying last 10 lines of logs for $service container:"
                    docker compose logs --tail=10 "$service"
                    all_healthy=false
                elif echo "$health_status" | grep -q "starting"; then
                    log_info "$service container health check is starting..."
                    all_healthy=false
                fi
            else
                log_warning "$service container may not be running properly"
                log_info "Displaying last 10 lines of logs for $service container:"
                docker compose logs --tail=100 "$service"
                all_healthy=false
            fi
        done

        if [ "$all_healthy" = true ]; then
            log_success "All services are running and healthy"
            break
        fi

        # Wait 10 seconds before the next check
        sleep 10
    done
}

# Function to run tests
run_tests() {
    log_info "Running tests to verify setup..."

    if task tests:all; then
        log_success "All tests passed! Environment setup completed successfully"
        return 0
    else
        log_warning "Some tests failed, but the environment is set up. Check the test output above."
        return 1
    fi
}

# Function to show available tasks
show_available_tasks() {
    log_success "Setup completed!"
    log_info "Available tasks for this project:"
    echo
    task --list-all
}

# Main function for application setup
setup_application() {
    show_header "Application Setup" "Building and starting the application"

    # Build Docker images
    if ! build_images; then
        exit 1
    fi

    # Start the application
    if ! start_application; then
        exit 1
    fi

    # Wait for services to be ready
    wait_for_services

    # Run tests
    run_tests

    # Show available tasks
    show_available_tasks

    show_completion "Application Setup"
}

setup_application "$@"
