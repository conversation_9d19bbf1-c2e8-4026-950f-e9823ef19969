#!/bin/bash

# toolkit.sh - Common utilities and functions for setup scripts
# This file contains shared functions used across different setup scripts

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Dock<PERSON> is running
is_docker_running() {
    docker info >/dev/null 2>&1
}

# Function to check network connectivity
check_network_connectivity() {
    log_info "Checking network connectivity..."
    
    # Test basic connectivity
    if ping -c 1 ******* >/dev/null 2>&1; then
        log_success "Network connectivity is working"
    else
        log_warning "Network connectivity to ******* failed"
        return 1
    fi
    
    # Test DNS resolution
    if nslookup github.com >/dev/null 2>&1; then
        log_success "DNS resolution is working"
    else
        log_warning "DNS resolution for github.com failed"
        log_info "Available DNS servers:"
        cat /etc/resolv.conf
        return 1
    fi
    
    return 0
}

# Function to detect operating system
detect_os() {
    case "$OSTYPE" in
        darwin*)
            echo "macos"
            ;;
        linux-gnu*|linux*)
            echo "linux"
            ;;
        *)
            if [ -f /etc/debian_version ] || [ -f /etc/ubuntu-release ]; then
                echo "linux"
            else
                echo "unknown"
            fi
            ;;
    esac
}

# Function to check if running in container
is_container() {
    [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null
}

# Function to check if sudo is available
has_sudo() {
    command_exists sudo && [ -n "$USER" ]
}

# Function to run command with or without sudo
run_with_sudo() {
    if has_sudo && ! is_container; then
        sudo "$@"
    else
        "$@"
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local check_command="$2"
    local max_retries="${3:-30}"
    local retry_interval="${4:-2}"

    log_info "Waiting for $service_name to be ready..."

    local retries=0
    while [ $retries -lt $max_retries ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            log_success "$service_name is ready"
            return 0
        fi

        retries=$((retries + 1))
        sleep $retry_interval
    done

    log_error "$service_name failed to start after $max_retries attempts"
    return 1
}

# Function to verify installation
verify_command() {
    local command_name="$1"
    local friendly_name="${2:-$command_name}"

    if command_exists "$command_name"; then
        log_success "$friendly_name is installed and available"
        return 0
    else
        log_error "$friendly_name is not installed or not in PATH"
        return 1
    fi
}

# Function to show script header
show_header() {
    local script_name="$1"
    local description="$2"

    echo
    log_info "=========================================="
    log_info "$script_name"
    log_info "$description"
    log_info "=========================================="
    echo
}

# Function to show completion message
show_completion() {
    local script_name="$1"

    echo
    log_success "=========================================="
    log_success "$script_name completed successfully!"
    log_success "=========================================="
    echo
}
